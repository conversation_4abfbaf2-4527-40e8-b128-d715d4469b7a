<?php

namespace App\Traits\POS;

use Exception;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Http\Integrations\POSPayment\Facades\POSPayment;
use App\Http\Integrations\VivaWallet\DataObject\Account;
use App\Http\Integrations\VivaWallet\Exceptions\SessionAlreadyExistException;
use App\Http\Integrations\VivaWallet\ValueObject\Invitation;

trait HasVivaWallet
{
    use HasOnlineVivaWallet;

    public function getOrCreateVivaAccount(): Account
    {
        if ($this->viva_account_id && $this->viva_merchant_id) {
            return new Account(
                accountId: $this->viva_account_id,
                merchantId: $this->viva_merchant_id,
                verified: true,
                acquiringEnabled: true,
                email: $this->email,
                created: now(),
                invitation: new Invitation($this->email, "", "", ""),
            );
        }

        if ($this->viva_account_id) {
            return new Account(
                accountId: $this->viva_account_id,
                merchantId: null,
                verified: false,
                acquiringEnabled: true,
                email: $this->email,
                created: now(),
                invitation: new Invitation($this->email, "", "", ""),
            );

            $account = POSPayment::account()->find($this->viva_account_id);

            if (!$this->viva_merchant_id) {
                $this->viva_merchant_id = $account->merchantId;
                $this->save();
            }

            return $account;
        }

        $account = POSPayment::account()->create($this->email);

        $this->viva_account_id = $account->accountId;
        $this->save();

        return $account;
    }

    public function vivaTerminals(): Collection
    {
        if (!$this->viva_account_id || !$this->viva_merchant_id) {
            throw new Exception(__('pos_strings.customer_need_to_connect'));
        }

        return POSPayment::account($this->viva_merchant_id)->devices()->all();
    }

    public function createOfflineVivaPayment(
        string $terminalId,
        int $amount,
        int $isvAmount,
        string $customerTrns,
        string $cashRegisterId
    ): string {
        if (!$this->viva_account_id || !$this->viva_merchant_id) {
            throw new Exception(__('pos_strings.customer_need_to_connect'));
        }

        $countryDetail = collect(config('stripe.country'))->filter(function ($country) {
            return  in_array($this->country, $country['countries']);
        })->first();

        if (!$countryDetail) {
            $countryDetail = collect(config('stripe.country'))->where('default', true)->first();
        }

        while (true) {
            $sessionId = Str::uuid();

            try {
                POSPayment::account($this->viva_merchant_id)
                    ->transactions()
                    ->create(
                        terminalId: $terminalId,
                        sessionId: $sessionId,
                        merchantReference: $sessionId,
                        cashRegisterId: $cashRegisterId,
                        amount: $amount,
                        currencyCode: (string) $countryDetail['code'],
                        customerTrns: $customerTrns,
                        isvAmount: $isvAmount,
                    );
                break;
            } catch (SessionAlreadyExistException $th) {
                continue;
            } catch (\Throwable $th) {
                throw $th;
            }
        }

        return $sessionId;
    }

    public function createOfflineVivaRefund(
        string $terminalId,
        string $cashRegisterId,
        string $parentSessionId,
        int $amount,
        ?string $customerTrns = '',
    ): string {
        if (!$this->viva_account_id || !$this->viva_merchant_id) {
            throw new Exception(__('pos_strings.customer_need_to_connect'));
        }

        $countryDetail = collect(config('stripe.country'))->filter(function ($country) {
            return  in_array($this->country, $country['countries']);
        })->first();

        if (!$countryDetail) {
            $countryDetail = collect(config('stripe.country'))->where('default', true)->first();
        }

        while (true) {
            $sessionId = Str::uuid();

            try {
                POSPayment::account($this->viva_merchant_id)
                    ->transactions()
                    ->refund(
                        terminalId: $terminalId,
                        sessionId: $sessionId,
                        merchantReference: $sessionId,
                        cashRegisterId: $cashRegisterId,
                        parentSessionId: $parentSessionId,
                        amount: $amount,
                        currencyCode: (string) $countryDetail['code'],
                        customerTrns: $customerTrns,
                    );
                break;
            } catch (SessionAlreadyExistException $th) {
                continue;
            } catch (\Throwable $th) {
                throw $th;
            }
        }

        return $sessionId;
    }

    function getAndUpdateTerminals()
    {
        return DB::transaction(function () {
            $terminals = $this->terminals()->latest()->withTrashed()->get();

            $pos_devices = $this->vivaTerminals()->reverse();

            foreach ($pos_devices as $key => $device) {
                $terminal = $terminals->where('virtual_terminal_id', $device->virtualTerminalId)->first();
                if (!$terminal) {
                    $current_device_id = $terminals->count() + 1;

                    $terminal = $this->terminals()->create([
                        'merchant_id' => $this->viva_merchant_id,
                        'status_id' => $device->statusId,
                        'source_code' => $device->sourceCode,
                        'terminal_id' => $device->terminalId,
                        'virtual_terminal_id' => $device->virtualTerminalId,
                        'nickname' => "{$this->company_name}'s Device $current_device_id",
                    ]);

                    $terminals->prepend($terminal);
                }

                if ($terminal->trashed()) {
                    $terminal->restore();
                }
            }

            $terminals
                ->whereNotIn('virtual_terminal_id', $pos_devices->pluck('virtualTerminalId')->values()->toArray())
                ->each(function ($terminal) {
                    $terminal->delete();
                });

            return $terminals->whereIn('virtual_terminal_id', $pos_devices->pluck('virtualTerminalId')->values()->toArray())->values()->all();
        });
    }
}
