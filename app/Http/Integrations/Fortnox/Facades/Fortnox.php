<?php

namespace App\Http\Integrations\Fortnox\Facades;

use App\Http\Integrations\Fortnox\Contracts\Fortnox as ContractsFortnox;
use Illuminate\Support\Facades\Facade;

/**
 *
 * @see \App\Http\Integrations\Fortnox\Contracts\Fortnox
 */

class Fortnox extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        self::clearResolvedInstance(ContractsFortnox::class);

        return ContractsFortnox::class;
    }
}
