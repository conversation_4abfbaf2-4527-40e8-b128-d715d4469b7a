<?php

namespace App\Http\Integrations\DailyCo\Requests;

use App\Http\Integrations\DailyCo\DataObject\Room;
use Saloon\Enums\Method;
use Saloon\Http\Request;
use Saloon\Contracts\Body\HasBody;
use Saloon\Contracts\Response;
use Saloon\Traits\Body\HasJsonBody;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class UpdateRoom extends Request implements HasBody
{
    use HasJsonBody, AlwaysThrowOnErrors;

    /**
     * Define the HTTP method
     *
     * @var Method
     */
    protected Method $method = Method::POST;

    /**
     * Define the endpoint for the request
     *
     * @return string
     */
    public function resolveEndpoint(): string
    {
        return "/rooms/$this->name";
    }

    public function __construct(
        protected string $name,
        protected ?string $privacy = 'public',
        protected ?array $properties = null,
    ) {}

    protected function defaultHeaders(): array
    {
        return [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    protected function defaultBody(): array
    {
        return [
            'privacy' => $this->privacy,
            'properties' => $this->properties,
        ];
    }

    public function createDtoFromResponse(Response $response): mixed
    {
        return Room::fromResponse($response);
    }
}
