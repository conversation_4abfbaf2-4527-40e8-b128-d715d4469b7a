<?php

namespace App\Http\Integrations\DailyCo\Resources;

use App\Http\Integrations\DailyCo\DataObject\Room;
use App\Http\Integrations\DailyCo\Requests\CreateRoom;
use App\Http\Integrations\DailyCo\Requests\DeleteRoom;
use App\Http\Integrations\DailyCo\Requests\UpdateRoom;
use Saloon\Contracts\Connector;

/**
 * @property Connector $connector
 */
class RoomResource extends Resource
{
    public function __construct(
        protected Connector $connector,
        protected ?string $room_name  = null,
    ) {}

    /**
     * Create a new room.
     *
     *
     * @return Room
     */
    public function create(
        ?string $name = null,
        ?string $privacy = 'public',
        ?array $properties = null,
    ): Room {
        $response = $this->connector
            ->send(new CreateRoom(
                name: $name,
                privacy: $privacy,
                properties: $properties,
            ));

        return $response->dto();
    }

    /**
     * Update a room.
     *
     *
     * @return Room
     */
    public function update(
        ?string $privacy,
        ?array $properties = null,
    ): Room {
        if (!$this->room_name) {
            throw new \Exception('Room name is required');
        }

        $response = $this->connector
            ->send(new UpdateRoom(
                name: $this->room_name,
                privacy: $privacy,
                properties: $properties,
            ));

        return $response->dto();
    }

    /**
     * Delete a new room.
     *
     *
     * @return array
     */
    public function delete(): array
    {
        if (!$this->room_name) {
            throw new \Exception('Room name is required');
        }

        $response = $this->connector
            ->send(new DeleteRoom(
                name: $this->room_name,
            ));

        return $response->dto();
    }


    /**
     * Meeting token resource
     *
     *
     * @return MeetingTokenResource
     */
    public function meetingToken(): MeetingTokenResource
    {
        if (!$this->room_name) {
            throw new \Exception('Room name is required');
        }

        return new MeetingTokenResource($this->connector, $this->room_name);
    }
}
