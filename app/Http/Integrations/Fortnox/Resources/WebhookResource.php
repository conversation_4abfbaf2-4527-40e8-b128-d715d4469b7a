<?php

namespace App\Http\Integrations\DailyCo\Resources;

use App\Http\Integrations\DailyCo\DataObject\Webhook;
use App\Http\Integrations\DailyCo\Requests\CreateWebhook;
use App\Http\Integrations\DailyCo\Requests\DeleteWebhook;
use App\Http\Integrations\DailyCo\Requests\FetchWebhook;
use Illuminate\Support\Collection;
use Saloon\Contracts\Connector;

/**
 * @property Connector $connector
 */
class WebhookResource extends Resource
{
    public function __construct(
        protected Connector $connector,
    ) {}

    /**
     * Create a webhook.
     *
     *
     * @return Webhook
     */
    public function create(
        string $url,
        // meeting.started
        // meeting.ended
        // participant.joined
        // participant.left
        // waiting-participant.joined
        // waiting-participant.left
        // recording.started
        // recording.ready-to-download
        // recording.error
        // streaming.started
        // streaming.updated
        // streaming.ended
        // streaming.error
        // batch-processor.job-finished
        // batch-processor.error
        // dialout.connected
        // dialout.answered
        // dialout.stopped
        // dialout.warning
        // dialout.error
        // dialin.connected
        // dialin.stopped
        // dialin.warning
        // dialin.error
        array $eventTypes,
    ): Webhook {
        $response = $this->connector
            ->send(new CreateWebhook(
                url: $url,
                eventTypes: $eventTypes,
            ));

        return $response->dto();
    }

    /**
     * Delete a webhook.
     *
     *
     * @return bool
     */
    public function delete(
        string $uuid,
    ): bool {
        $response = $this->connector
            ->send(new DeleteWebhook(
                uuid: $uuid,
            ));

        return $response->dto();
    }

    /**
     * Fetch all webhook.
     *
     *
     * @return Collection
     */
    public function list(): Collection
    {
        $response = $this->connector
            ->send(new FetchWebhook());

        return $response->dto();
    }
}
